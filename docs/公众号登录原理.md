# 微信公众号二维码登录原理


页面展示的二维码图片地址如下:
```
https://mp.weixin.qq.com/cgi-bin/scanloginqrcode?action=getqrcode&random=
```
其中，`random`参数为系统当前的时间戳:
```js
const random = new Date().getTime()
```

该链接返回的二维码图片内部编码了一个 url 地址，如下所示:
```
http://mp.weixin.qq.com/mp/scanlogin?action=index&qrticket=b5e94c8689978a087986ef0bb00fe2ea&scanscene=0#wechat_redirect
```


页面加载时，首先调用`getIngorePassList`接口，参数如下:
```http request
POST /cgi-bin/bizlogin
Host: https://mp.weixin.qq.com

action=prelogin
```
根据返回结果，会调用`report`接口，如下:
```http request
POST /cgi-bin/webreport
Host: https://mp.weixin.qq.com

reportJson={"devicetype":1,"newsessionid":"172059629456827","optype":1,"page_state":3,"log_id":19015}
```

`newsessionid`的取值逻辑如下:
```js
this.sessionid = new Date().getTime() + "" + Math.floor(Math.random() * 100);
```

然后调用`this.getQrcode()`方法获取二维码，如下:
```http request
POST /cgi-bin/bizlogin?action=startlogin
Host: https://mp.weixin.qq.com

userlang=zh_CN
redirect_url=
login_type=3
sessionid=sessionid
```
接口返回:
```json
{"base_resp":{"err_msg":"ok","ret":0}}
```
表示正常。

此时页面会把`this.hasStartLogin`标志改为`true`，表示登录流程已开始。同时调用`this.refreshQrcode()`获取二维码图片并展示

未扫码：
```json
{
  "acct_size": 0,
  "base_resp": {
    "err_msg": "ok",
    "ret": 0
  },
  "binduin": 0,
  "status": 0,
  "user_category": 0
}
```

已扫码：
```json
{
  "acct_size": 19,
  "base_resp": {
    "err_msg": "ok",
    "ret": 0
  },
  "binduin": 0,
  "status": 4,
  "user_category": 0
}
```

已登录：
```json
{
  "acct_size": 19,
  "base_resp": {
    "err_msg": "ok",
    "ret": 0
  },
  "binduin": 0,
  "status": 1,
  "user_category": 2
}
```
