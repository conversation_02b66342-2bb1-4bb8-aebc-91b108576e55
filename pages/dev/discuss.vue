<template>
  <div style="max-width: 667px;margin: 0 auto;padding: 10px 10px 80px;">
    <p style="font-size: 15px;color: #949494;">留言 {{ totalCount }}</p>
    <div style="margin-top: -10px;">
      <div style="margin-top: 25px;" v-for="comment in targetCommentData.elected_comment">
        <div style="display: flex;">
          <img v-if="[1, 2].includes(comment.identity_type)" :src="comment.logo_url"
               style="display: block;width: 30px;height: 30px;border-radius: 50%;margin-right: 8px;" alt="">
          <img v-else :src="comment.logo_url"
               style="display: block;width: 30px;height: 30px;border-radius: 2px;margin-right: 8px;" alt="">
          <div style="flex: 1;">
            <p style="display: flex;line-height: 16px;margin-bottom: 5px;">
              <span style="margin-right: 5px;font-size: 15px;color: #949494;">{{ comment.nick_name }}</span>
              <span
                  style="margin-right: 5px;font-size: 12px;color: #b5b5b5;">{{ comment.ip_wording.province_name }}</span>
              <span style="font-size: 12px;color: #b5b5b5;">{{ formatAlbumTime(comment.create_time) }}</span>
              <span style="flex: 1;"></span>
              <span style="display: inline-flex;align-items: center; font-size: 12px;color: #b5b5b5;">
              <svg style="margin-right: 5px;" width="12" height="12" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                      d="M7.56162 1.16952C7.66569 1.09661 7.78195 1.06124 7.88247 1.0912C7.97653 1.11923 8.23851 1.25916 8.50988 1.96799C8.64419 2.31881 8.9356 3.2424 8.42155 5.05431C8.29751 5.49152 8.61394 5.95303 9.09259 5.95971L12.492 6.00716L12.492 6.00721H12.4991C12.6049 6.00721 12.7228 6.01986 12.8134 6.05898C12.8544 6.07671 12.8815 6.09639 12.8999 6.116C12.9166 6.13375 12.9368 6.16247 12.9515 6.21636C12.9848 6.33784 13.0228 6.74712 12.9473 7.42262C12.874 8.07857 12.698 8.94479 12.341 9.9598C12.0424 10.8088 11.6601 11.5292 11.0684 12.4879C11.0558 12.5052 11.0462 12.5197 11.0418 12.5265L11.0404 12.5285C11.0292 12.5454 11.0242 12.5531 11.018 12.5618C11.0076 12.5764 11.0018 12.582 10.9983 12.585C10.996 12.587 10.9908 12.5912 10.9777 12.5959C10.9638 12.6009 10.9311 12.61 10.8706 12.61H4.56278L4.56373 5.58489C4.87126 5.41901 5.19881 5.20128 5.54112 4.84059C5.93883 4.42152 6.33789 3.8294 6.76254 2.94183C6.84974 2.75957 6.91745 2.55962 6.97574 2.37762C6.99264 2.32486 7.0087 2.27379 7.02438 2.22393L7.02439 2.22389C7.066 2.09158 7.10495 1.96776 7.14985 1.84312C7.2758 1.49352 7.40247 1.28101 7.56162 1.16952ZM9.45205 1.60729C9.13229 0.772086 8.70208 0.282772 8.17063 0.124374C7.64564 -0.0320981 7.20308 0.188912 6.98278 0.343248C6.55169 0.64525 6.33837 1.11908 6.20071 1.5012C6.14817 1.64705 6.10002 1.80016 6.05661 1.93824C6.0422 1.98405 6.02832 2.02821 6.01496 2.0699C5.95791 2.24804 5.90763 2.39115 5.85248 2.50643C5.45683 3.3334 5.1121 3.8271 4.80935 4.14611C4.51322 4.45815 4.23983 4.6219 3.9473 4.76821C3.71095 4.88641 3.55494 5.12906 3.55491 5.40159L3.55388 12.9125C3.55383 13.3026 3.87002 13.6188 4.26008 13.6188H10.8706C11.2097 13.6188 11.4663 13.5113 11.6519 13.3535C11.7387 13.2797 11.7988 13.2043 11.8387 13.1484C11.8556 13.1248 11.8704 13.1025 11.8786 13.09L11.8813 13.0859L11.8826 13.0839L11.8955 13.0685L11.9142 13.0382C12.5304 12.0414 12.9578 11.247 13.2927 10.2945C13.6745 9.20895 13.8679 8.26811 13.9499 7.5347C14.0297 6.82084 14.009 6.25845 13.9246 5.95014C13.805 5.51285 13.5104 5.26112 13.2134 5.13284C12.9385 5.01407 12.661 4.99859 12.5028 4.99836L9.49071 4.95631C9.92962 3.15791 9.64796 2.11902 9.45205 1.60729ZM0.000800636 5.46783C-0.0181914 5.0652 0.303128 4.72836 0.706212 4.72836H1.75264C2.14266 4.72836 2.45883 5.04454 2.45883 5.43456V12.9442C2.45883 13.3342 2.14266 13.6504 1.75264 13.6504H1.06044C0.68335 13.6504 0.372791 13.3541 0.355024 12.9775L0.000800636 5.46783Z"
                      fill="#b5b5b5"></path>
              </svg>
              <span v-if="comment.like_num">{{ comment.like_num }}</span>
            </span>
            </p>
            <p style="font-size: 15px;color: #333;white-space: pre-line;" v-html="comment.content"></p>
          </div>
        </div>
        <div v-if="comment.reply_new && comment.reply_new.reply_list.length > 0" style="padding-left: 38px;">
          <div style="display: flex;margin-top: 15px;" v-for="reply in comment.reply_new.reply_list">
            <img v-if="[1, 2].includes(reply.identity_type)" :src="reply.logo_url"
                 style="display: block;width: 23px;height: 23px;border-radius: 50%;margin-right: 8px;" alt="">
            <img v-else :src="reply.logo_url"
                 style="display: block;width: 23px;height: 23px;border-radius: 2px;margin-right: 8px;" alt="">
            <div style="flex: 1;">
              <p style="display: flex;line-height: 16px;margin-bottom: 5px;">
                <span style="margin-right: 5px;font-size: 15px;color: #949494;">{{ reply.nick_name }}</span>
                <span
                    style="margin-right: 5px;font-size: 12px;color: #b5b5b5;">{{ reply?.ip_wording?.province_name }}</span>
                <span style="font-size: 12px;color: #b5b5b5;">{{ formatAlbumTime(reply.create_time) }}</span>
                <span style="flex: 1;"></span>
                <span style="display: inline-flex;align-items: center; font-size: 12px;color: #b5b5b5;">
                <svg style="margin-right: 5px;" width="12" height="12" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path fill-rule="evenodd" clip-rule="evenodd"
                        d="M7.56162 1.16952C7.66569 1.09661 7.78195 1.06124 7.88247 1.0912C7.97653 1.11923 8.23851 1.25916 8.50988 1.96799C8.64419 2.31881 8.9356 3.2424 8.42155 5.05431C8.29751 5.49152 8.61394 5.95303 9.09259 5.95971L12.492 6.00716L12.492 6.00721H12.4991C12.6049 6.00721 12.7228 6.01986 12.8134 6.05898C12.8544 6.07671 12.8815 6.09639 12.8999 6.116C12.9166 6.13375 12.9368 6.16247 12.9515 6.21636C12.9848 6.33784 13.0228 6.74712 12.9473 7.42262C12.874 8.07857 12.698 8.94479 12.341 9.9598C12.0424 10.8088 11.6601 11.5292 11.0684 12.4879C11.0558 12.5052 11.0462 12.5197 11.0418 12.5265L11.0404 12.5285C11.0292 12.5454 11.0242 12.5531 11.018 12.5618C11.0076 12.5764 11.0018 12.582 10.9983 12.585C10.996 12.587 10.9908 12.5912 10.9777 12.5959C10.9638 12.6009 10.9311 12.61 10.8706 12.61H4.56278L4.56373 5.58489C4.87126 5.41901 5.19881 5.20128 5.54112 4.84059C5.93883 4.42152 6.33789 3.8294 6.76254 2.94183C6.84974 2.75957 6.91745 2.55962 6.97574 2.37762C6.99264 2.32486 7.0087 2.27379 7.02438 2.22393L7.02439 2.22389C7.066 2.09158 7.10495 1.96776 7.14985 1.84312C7.2758 1.49352 7.40247 1.28101 7.56162 1.16952ZM9.45205 1.60729C9.13229 0.772086 8.70208 0.282772 8.17063 0.124374C7.64564 -0.0320981 7.20308 0.188912 6.98278 0.343248C6.55169 0.64525 6.33837 1.11908 6.20071 1.5012C6.14817 1.64705 6.10002 1.80016 6.05661 1.93824C6.0422 1.98405 6.02832 2.02821 6.01496 2.0699C5.95791 2.24804 5.90763 2.39115 5.85248 2.50643C5.45683 3.3334 5.1121 3.8271 4.80935 4.14611C4.51322 4.45815 4.23983 4.6219 3.9473 4.76821C3.71095 4.88641 3.55494 5.12906 3.55491 5.40159L3.55388 12.9125C3.55383 13.3026 3.87002 13.6188 4.26008 13.6188H10.8706C11.2097 13.6188 11.4663 13.5113 11.6519 13.3535C11.7387 13.2797 11.7988 13.2043 11.8387 13.1484C11.8556 13.1248 11.8704 13.1025 11.8786 13.09L11.8813 13.0859L11.8826 13.0839L11.8955 13.0685L11.9142 13.0382C12.5304 12.0414 12.9578 11.247 13.2927 10.2945C13.6745 9.20895 13.8679 8.26811 13.9499 7.5347C14.0297 6.82084 14.009 6.25845 13.9246 5.95014C13.805 5.51285 13.5104 5.26112 13.2134 5.13284C12.9385 5.01407 12.661 4.99859 12.5028 4.99836L9.49071 4.95631C9.92962 3.15791 9.64796 2.11902 9.45205 1.60729ZM0.000800636 5.46783C-0.0181914 5.0652 0.303128 4.72836 0.706212 4.72836H1.75264C2.14266 4.72836 2.45883 5.04454 2.45883 5.43456V12.9442C2.45883 13.3342 2.14266 13.6504 1.75264 13.6504H1.06044C0.68335 13.6504 0.372791 13.3541 0.355024 12.9775L0.000800636 5.46783Z"
                        fill="#b5b5b5"></path>
                </svg>
                <span v-if="reply.reply_like_num">{{ reply.reply_like_num }}</span>
              </span>
              </p>
              <p style="font-size: 15px;color: #333;white-space: pre-line;">{{ reply.content }}</p>
            </div>
          </div>
        </div>
        <p style="display: flex;align-items: center; font-size: 14px;color: #a3a0a0;padding-left: 38px;padding-top: 5px;"
           v-if="comment.reply_new.reply_total_cnt - comment.reply_new.reply_list.length > 0">
          <span>{{ comment.reply_new.reply_total_cnt - comment.reply_new.reply_list.length }}条回复</span>
          <img src="https://wxa.wxs.qq.com/images/wxapp/feedback_icon.png" alt=""
               style="filter: invert(1);width: 10px;height: 6px;margin-left: 5px;">
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {formatAlbumTime} from "~/utils/album";

definePageMeta({
  layout: false
})

const IdentityTypeMap = {
  0: '个人微信号',
  1: '圆头像',
  2: '圆头像',
  3: '未知',
}


const commentData = {
  "base_resp": {
    "exportkey_token": "",
    "ret": 0
  },
  "buffer": "GCMwAA==",
  "continue_flag": true,
  "elected_comment": [
    {
      "author_like_status": 0,
      "content": "照顾老年人的机器人这真的是巨大的蓝海，毕竟照顾不能自理的老人，不管道德上的约束有多强，时间一久一定是会让你精疲力尽，“久病床前无孝子”不是一句假话。机器人照顾至少理论上靠谱，老人的生活习惯相对比较固定，且没有太大的生活半径，程序编码应该相对简单，且机器人也永远不会产生厌烦和倦怠情绪。",
      "content_id": "7512078537222783054",
      "create_time": 1728608257,
      "id": 9,
      "identity_name": "2",
      "identity_type": 3,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "浙江"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 48,
      "like_status": 0,
      "logo_url": "https://wx.qlogo.cn/mmopen/vi_32/Q3auHgzwzM7PpIFNNvE1lsyK5ETarw6wPPYqQqpMutnbUM1Q2VsNd31qG9OemVMBRkpdrbNPxubFfUsnjOl0Aw/64",
      "my_id": 78,
      "nick_name": "神经蛙",
      "openid": "owOYjs9L-saDR5vRUM0MyfqWWNks",
      "reply_new": {
        "max_reply_id": 10,
        "reply_list": [
          {
            "author_like_status": 0,
            "content": "成本问题，国内雇人成本高，穷人用不上，有钱人不需要",
            "create_time": 1728608925,
            "identity_name": "2",
            "identity_type": 3,
            "ip_wording": {
              "city_id": "",
              "city_name": "",
              "country_id": "156",
              "country_name": "中国",
              "province_id": "",
              "province_name": "山东"
            },
            "is_deleted": 0,
            "is_from": 3,
            "is_from_friend": 0,
            "logo_url": "https://wx.qlogo.cn/mmopen/vi_32/Q3auHgzwzM7PpIFNNvE1lsyK5ETarw6w764u81T2iaqQGf9xYB2TuiciaCqFM8H0Q6yn0TvVkb6QA4xq7MUTHEOtA/64",
            "nick_name": "哄哄",
            "openid": "owOYjs9AbLhu4iuLX_01KV5BnIyc",
            "reply_del_flag": 0,
            "reply_id": 1,
            "reply_is_elected": 1,
            "reply_like_num": 10,
            "reply_like_status": 0
          },
          {
            "author_like_status": 0,
            "content": "汽车刚出现的时候也只是有钱人的玩具，老龄化大势所趋，这个市场非常大，再加上AI的发展，只要能量产，我相信成本就能下来。",
            "create_time": 1728609965,
            "identity_name": "2",
            "identity_type": 3,
            "ip_wording": {
              "city_id": "",
              "city_name": "",
              "country_id": "156",
              "country_name": "中国",
              "province_id": "",
              "province_name": "浙江"
            },
            "is_deleted": 0,
            "is_from": 3,
            "is_from_friend": 0,
            "logo_url": "https://wx.qlogo.cn/mmopen/vi_32/Q3auHgzwzM7PpIFNNvE1lsyK5ETarw6wPPYqQqpMutnbUM1Q2VsNd31qG9OemVMBRkpdrbNPxubFfUsnjOl0Aw/64",
            "nick_name": "神经蛙",
            "openid": "owOYjs9L-saDR5vRUM0MyfqWWNks",
            "reply_del_flag": 0,
            "reply_id": 4,
            "reply_is_elected": 1,
            "reply_like_num": 28,
            "reply_like_status": 0,
            "to_nick_name": "哄哄"
          }
        ],
        "reply_total_cnt": 9
      }
    },
    {
      "author_like_status": 0,
      "content": "国内行不通，996牛马们，下班累都累死了，还打什么乒乓球",
      "content_id": "10657231118979301445",
      "create_time": 1728609518,
      "id": 14,
      "identity_name": "owOYjs5qmQe_KD5nMF9ueAgZI04A",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "浙江"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 26,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/8MnTI7HjgENujwTXhictB6gBPSF2oPOC5H72ia6sE6l3q9FpZUBEibmMU2BOjX26gfARfJHMHCb4vCoDoA1l2hozDDUzKNnX9TQ/64",
      "my_id": 69,
      "nick_name": "Javyn",
      "openid": "owOYjs5qmQe_KD5nMF9ueAgZI04A",
      "reply_new": {
        "max_reply_id": 7,
        "reply_list": [
          {
            "author_like_status": 0,
            "content": "而且商业房租很高的",
            "create_time": 1728611349,
            "identity_name": "owOYjs3wNKi0hlTlvCoRRz7EwXbU",
            "identity_type": 0,
            "ip_wording": {
              "city_id": "",
              "city_name": "",
              "country_id": "156",
              "country_name": "中国",
              "province_id": "",
              "province_name": "北京"
            },
            "is_deleted": 0,
            "is_from": 3,
            "is_from_friend": 0,
            "logo_url": "http://wx.qlogo.cn/mmopen/PiajxSqBRaEIJWOEemuGFET1vzYuQz6Wia7cjvTOoQb4q87RqW7yLhiajh4uOOvdicCM1qJ1ZwibMc5ibMGBa7oPvSaJmBgRWZD111z9QIoQPN7u5bK25T3QOAABEwM7xzrJFB/64",
            "nick_name": "小锁@jierui",
            "openid": "owOYjs3wNKi0hlTlvCoRRz7EwXbU",
            "reply_del_flag": 0,
            "reply_id": 1,
            "reply_is_elected": 1,
            "reply_like_num": 12,
            "reply_like_status": 0
          }
        ],
        "reply_total_cnt": 5
      }
    },
    {
      "author_like_status": 0,
      "content": "汽车那句话不对，马路轮胎的声音其实更大",
      "content_id": "11264289093745377298",
      "create_time": 1728608357,
      "id": 10,
      "identity_name": "owOYjs7eMypzym3J23BmrYGuKQH4",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": ""
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 21,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/A0017IuWaqyYlnNhaa8TnlwEAdIia3NJoPPlqu5rtsNiaticZTT8MkAq08ECQlCYEJ5aMsvlSuic5mzLGTA3IcLciaeGctSzibOLtcdOmkkXhS9M9AicahZqXzHJcGiaicmRHja3U/64",
      "my_id": 18,
      "nick_name": "。🙃",
      "openid": "owOYjs7eMypzym3J23BmrYGuKQH4",
      "reply_new": {
        "max_reply_id": 3,
        "reply_list": [
          {
            "author_like_status": 0,
            "content": "是减速带➕大货车，三轮车",
            "create_time": 1728633347,
            "identity_name": "owOYjs4tgtvAi3ifLuwpVQE-n0hc",
            "identity_type": 0,
            "ip_wording": {
              "city_id": "",
              "city_name": "",
              "country_id": "156",
              "country_name": "中国",
              "province_id": "",
              "province_name": ""
            },
            "is_deleted": 0,
            "is_from": 3,
            "is_from_friend": 0,
            "logo_url": "http://wx.qlogo.cn/mmopen/8MnTI7HjgENMg5yMNiapaydoxSozyaeRWtFZ6kOwiaO0yJSLicvic52Wgficdc9HJ3mvWJAOkahLV0MRZC2lPB66ObibkeQgYYkq8vLAnyLCs6OEUwGHELOMuLFD8WFtc0BTg3/64",
            "nick_name": "Realme",
            "openid": "owOYjs4tgtvAi3ifLuwpVQE-n0hc",
            "reply_del_flag": 0,
            "reply_id": 2,
            "reply_is_elected": 1,
            "reply_like_num": 2,
            "reply_like_status": 0
          }
        ],
        "reply_total_cnt": 2
      }
    },
    {
      "author_like_status": 0,
      "content": "我觉得乒乓仓的想法特别好，但是和国内目前大环境不太匹配。老美有严格的上班时长，有些州的工作午饭和午休时间也是算入一天的工作时间的。但现在国内的工作环境都在挤压和占用员工的休息时间，很多工作都要加班，这导致走出去的消费娱乐需求减少，通过手机上消费娱乐需求增加（短视频、游戏、刷剧）。",
      "content_id": "2141233504216678515",
      "create_time": 1728611502,
      "id": 21,
      "identity_name": "owOYjs0m-yw_5PF4Vn3aMEBK3aOI",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "福建"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 20,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/OU2rqvx645s3FCc1PAX7gVA9qL5W40cDKib3uibLjicU10vrX1EESnSjWEVAtsKLV7wY7m1f4WvOpzibDfRicVpmK8Q/64",
      "my_id": 115,
      "nick_name": "丁丁",
      "openid": "owOYjs0m-yw_5PF4Vn3aMEBK3aOI",
      "reply_new": {
        "max_reply_id": 1,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    },
    {
      "author_like_status": 0,
      "content": "国内已经有了吧，台球店",
      "content_id": "4902550210258928215",
      "create_time": 1728607194,
      "id": 1,
      "identity_name": "v2_060000231003b20faec8c6e6811ec2d5cf07e832b07700d79ec9755bc74bc360c612df19c681@finder",
      "identity_type": 2,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "广东"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 15,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/finderhead/PZI7pLaVibDMSgicFSrN464ibft19KyU96Tqg1YiaibANkkQaMOnz33IMCxRZUIkM6SgCZV8B85MWyCo/64",
      "my_id": 599,
      "nick_name": "我超爱吃豆腐",
      "openid": "owOYjswzpzfEJ81dvUjs23BuCduU",
      "reply_new": {
        "max_reply_id": 8,
        "reply_list": [
          {
            "author_like_status": 0,
            "content": "无人麻将馆",
            "create_time": **********,
            "identity_name": "owOYjs_2uv3yr_HSUD51a2_fr2uo",
            "identity_type": 0,
            "ip_wording": {
              "city_id": "",
              "city_name": "",
              "country_id": "156",
              "country_name": "中国",
              "province_id": "",
              "province_name": "北京"
            },
            "is_deleted": 0,
            "is_from": 3,
            "is_from_friend": 0,
            "logo_url": "http://wx.qlogo.cn/mmopen/PiajxSqBRaEJsSXbqjPMZON5KsFpksJBiaMeIqRKjJUfM0qYKsA33f5w4MqQvZD27ZPEbnO5n8aGZE8xEqYKZU1RpOTf2uTNB9SmcgHBcO5FnpWLS2xnG6LLQIJUPXrRMY/64",
            "nick_name": "辉",
            "openid": "owOYjs_2uv3yr_HSUD51a2_fr2uo",
            "reply_del_flag": 0,
            "reply_id": 3,
            "reply_is_elected": 1,
            "reply_like_num": 7,
            "reply_like_status": 0
          }
        ],
        "reply_total_cnt": 7
      }
    },
    {
      "author_like_status": 0,
      "content": "公园有很多免费的乒乓球桌，24小时开放[嘿哈]",
      "content_id": "2060963314233507963",
      "create_time": 1728608139,
      "id": 7,
      "identity_name": "owOYjs3bkK-Dh8mgzr-6zmWNNrk8",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "广东"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 14,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/ajNVdqHZLLAUDdFkhLFk762wWgWN9rgr2hzib1Xxfm4EIxYZT9LmL6HQjr43h8X0GaaSic5XRv5MuzNdG73DPG5B4luAJD180IvYnSNnPeas5DbdPvQs1xuC3yhR1A8xKF/64",
      "my_id": 123,
      "nick_name": "失眠",
      "openid": "owOYjs3bkK-Dh8mgzr-6zmWNNrk8",
      "reply_new": {
        "max_reply_id": 3,
        "reply_list": [
          {
            "author_like_status": 0,
            "content": "有风不好打",
            "create_time": 1728609139,
            "identity_name": "owOYjs9GkP5D1iut3m3xykPGn5lY",
            "identity_type": 0,
            "ip_wording": {
              "city_id": "",
              "city_name": "",
              "country_id": "156",
              "country_name": "中国",
              "province_id": "",
              "province_name": "山西"
            },
            "is_deleted": 0,
            "is_from": 3,
            "is_from_friend": 0,
            "logo_url": "http://wx.qlogo.cn/mmopen/A0017IuWaqyfZsficW0JjnyTmdvWCG9hQJBDXKGnz713q4aIOsxV0qjsQkt7M0aeKVaibBLlkpF90ShABHE5PWA4ntWVtGhAKWglL4BWW4aBTY4bibjZcAakTwoLVMOEkiaK/64",
            "nick_name": "东路",
            "openid": "owOYjs9GkP5D1iut3m3xykPGn5lY",
            "reply_del_flag": 0,
            "reply_id": 1,
            "reply_is_elected": 1,
            "reply_like_num": 6,
            "reply_like_status": 0
          }
        ],
        "reply_total_cnt": 2
      }
    },
    {
      "author_like_status": 0,
      "content": "江浙一带的某些银行已经把下班后闲置的网点空间共享了，比如会议室给微小企业会客，还有展区吧台等。",
      "content_id": "11529616367812608359",
      "create_time": 1728609534,
      "id": 15,
      "identity_name": "owOYjsy9EN6PFP9l9Kws5dW390tI",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "新疆"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 11,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/A0017IuWaqwMsDhKLw0HyCjm5V0h0qA4o3bgGRkfCl1K7chibwvXHKSTWC5FskOiaSJ0onajUaiafwZ0W5DicADJzGxfphv6dy1B/64",
      "my_id": 359,
      "nick_name": "发现~",
      "openid": "owOYjsy9EN6PFP9l9Kws5dW390tI",
      "reply_new": {
        "max_reply_id": 1,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    },
    {
      "author_like_status": 0,
      "content": "对某一空间某一段时间的使用权，可以做很多事",
      "content_id": "13418510647784636829",
      "create_time": 1728607930,
      "id": 5,
      "identity_name": "owOYjs7Ti1es3Yd8_k9uoWBbPDUQ",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "上海"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 9,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/A0017IuWaqypRApfxKNj43tNTqLCbxGuEUibmicWRj0F8kysicUibM6vl3vlHnicMIddNr6yocB8rlYsY0EacZNxyWXKh4621u46LqhDYedd23aGNoUxHmhXLssZlx67xeJe5/64",
      "my_id": 413,
      "nick_name": "Kylex不喝酒",
      "openid": "owOYjs7Ti1es3Yd8_k9uoWBbPDUQ",
      "reply_new": {
        "max_reply_id": 2,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    },
    {
      "author_like_status": 0,
      "content": "望远镜口径始终是越大越好，不然各种侦查卫星的口径为什么越来越大。大型光学望远镜也可以软件处理，多曝光叠加合成。不过光学望远镜的重要性也远不如前，不如更精准波段的其他类型望远镜",
      "content_id": "10258688390480265400",
      "create_time": 1728607598,
      "id": 3,
      "identity_name": "owOYjs7seNm6eHKhX61tdwX-I1o4",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": ""
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 8,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/Q3auHgzwzM5cBmpOAglhkv4CodzbCJzYNva2Y9Wrzlbias7c8ic6TCbmu8k2FbHiaunGI3C3ndyvTSDicRqvbQC8Jsj7l9wiaTccehrwOGOTokAA/64",
      "my_id": 184,
      "nick_name": "空桑",
      "openid": "owOYjs7seNm6eHKhX61tdwX-I1o4",
      "reply_new": {
        "max_reply_id": 1,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    },
    {
      "author_like_status": 0,
      "content": "值得注意的是，这一期的内容有5段关于太空的话题。",
      "content_id": "5316500373723152575",
      "create_time": 1728611435,
      "id": 20,
      "identity_name": "owOYjszEuXcYHaiWGB4di1O7GQ8s",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "广东"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 6,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/BVolsXtGvK6QpPKibkDSoibPkPpU4brXjMFib4BQcl5ctmialvXv0JgbGNkYqFoeMibCFHGGCib1EVaoUVib7mApHxE4OU6ksbGkCMvlaLLQTd3W9yH1pVBnkqyomP8uic4moEW1/64",
      "my_id": 191,
      "nick_name": "Sky&Sea",
      "openid": "owOYjszEuXcYHaiWGB4di1O7GQ8s",
      "reply_new": {
        "max_reply_id": 1,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    },
    {
      "author_like_status": 0,
      "content": "国内想租店面创业，赚钱的往往是房东和装修包工队。",
      "content_id": "5851961574377390184",
      "create_time": **********,
      "id": 11,
      "identity_name": "owOYjs9LSAGToEipPj8dINhSB_0s",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "江苏"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 6,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/BVolsXtGvK7uQE5LAD7ibEVDTYiaceXk79Pbt8ENfls6APovj5jmKR7PO8rI45hsmu2446GJhGdJadq7bz61iaW9zfEqP8PWFfh/64",
      "my_id": 104,
      "nick_name": "小波",
      "openid": "owOYjs9LSAGToEipPj8dINhSB_0s",
      "reply_new": {
        "max_reply_id": 1,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    },
    {
      "author_like_status": 0,
      "content": "技术压根没能改善人类的生活，昙花一现的技术突破只不过是杯水车薪。疫苗的发明也伴随着医疗资源的愈发不平等，互联网的现在也不过是把开放演绎成一个个APP接口，即便是AI也只会把过去三十年学习来的人类偏见继续繁衍给未来三十年的人～&nbsp;不知道该如何形容这样的事情，似乎“发展”不再是褒义词了，当地球气候都如此堪忧的时刻，一切说辞都站不住脚",
      "content_id": "10106300713140224225",
      "create_time": **********,
      "id": 24,
      "identity_name": "owOYjs3X271NuaisWKC00id4XD70",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "上海"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 2,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/W5kQLp9rng6jAeEGCRDn0r6I1UAaOpobkVM1fGc0a4trgxx2ulGYqgCQIdrhMR87X0sEWk5EtK2oXLEia72WaaksrI558VtlZ/64",
      "my_id": 225,
      "nick_name": "牧",
      "openid": "owOYjs3X271NuaisWKC00id4XD70",
      "reply_new": {
        "max_reply_id": 3,
        "reply_list": [
          {
            "author_like_status": 0,
            "content": "纵向比较绝对是改善了，人均寿命，人口数量，工作时间，这些显而易见的指标哪怕只比50年前都大大改善了。当然，幸福感这种主观的东西确实不好说，跟社会公平关系太大了。",
            "create_time": 1728626647,
            "identity_name": "2",
            "identity_type": 3,
            "ip_wording": {
              "city_id": "",
              "city_name": "",
              "country_id": "156",
              "country_name": "中国",
              "province_id": "",
              "province_name": "浙江"
            },
            "is_deleted": 0,
            "is_from": 3,
            "is_from_friend": 0,
            "logo_url": "https://wx.qlogo.cn/mmopen/vi_32/Q3auHgzwzM7PpIFNNvE1lsyK5ETarw6wPPYqQqpMutnbUM1Q2VsNd31qG9OemVMBRkpdrbNPxubFfUsnjOl0Aw/64",
            "nick_name": "神经蛙",
            "openid": "owOYjs9L-saDR5vRUM0MyfqWWNks",
            "reply_del_flag": 0,
            "reply_id": 2,
            "reply_is_elected": 1,
            "reply_like_num": 4,
            "reply_like_status": 0
          }
        ],
        "reply_total_cnt": 2
      }
    },
    {
      "author_like_status": 0,
      "content": "电动汽车的胎噪也不小啊",
      "content_id": "1120113447728578640",
      "create_time": 1728612168,
      "id": 23,
      "identity_name": "owOYjs0habZ8JpJ2kbTyD-ZCKWNw",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "四川"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 4,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/PiajxSqBRaEJicURUMQeib3YyNd5DfibeCkeQc9SpEXh1l8QTicIEkPLica1ibPZ9xQLtHIZoDDz6icHoDQRIt7AicBp8lA/64",
      "my_id": 80,
      "nick_name": "禹鱼",
      "openid": "owOYjs0habZ8JpJ2kbTyD-ZCKWNw",
      "reply_new": {
        "max_reply_id": 2,
        "reply_list": [],
        "reply_total_cnt": 1
      }
    },
    {
      "author_like_status": 0,
      "content": "国内有无人值守自习室",
      "content_id": "13383654965249905",
      "create_time": 1728608996,
      "id": 12,
      "identity_name": "gh_97e73700e4b1",
      "identity_type": 1,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "广东"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 4,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM4ib3dlFWVTPM7IX8akTibvyT9JCdYHCxp0CeU0Rc5tngEA/64",
      "my_id": 1905,
      "nick_name": "今天八杯水",
      "openid": "owOYjszJbEYBV-znZ5mVTofLx74s",
      "reply_new": {
        "max_reply_id": 2,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    },
    {
      "author_like_status": 0,
      "content": "中美环境不一样，美国人工高土地便宜，所以批量节省人工的模式会有效果；中国人工便宜土地贵，所以在热闹的商业区，去做共享空间这种节省土地成本的模式更好，而空置的不发达商业空间，可以做“小蓄水池”类型的模式，例如微型仓库（蓄物）、微型酒店（蓄人）",
      "content_id": "2708809931984732495",
      "create_time": 1728629419,
      "id": 31,
      "identity_name": "owOYjsx4OQmplvcTpsEGIyyapjZs",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "湖北"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 3,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/ajNVdqHZLLBeXMlZyQRHbic7YNRXQrWnCLxhjM1U1YJuy7DzgwBzhmqMJzzAkolnAsraW20QDoElCMoPDNQKPy8X2RMqjMZckykS6LLGCRxAFFVwhib7Sk2qDHygMVBj6h/64",
      "my_id": 335,
      "nick_name": "简单的力量",
      "openid": "owOYjsx4OQmplvcTpsEGIyyapjZs",
      "reply_new": {
        "max_reply_id": 2,
        "reply_list": [],
        "reply_total_cnt": 1
      }
    },
    {
      "author_like_status": 0,
      "content": "我自己是机器人工程师，也想过创业养老机器人这个方向。但是经过发达国家的调研，我还是打消了这个念头。倒不是技术难点，而也是伦理道德。我们都觉得很容易接受机器人的陪伴，其实人性并不会，机器人终究是机器没有温度。好多欧美爷爷奶奶给我反馈，他们能看到年轻的面庞就格外开心，非常抵触机器人。",
      "content_id": "10184674030817116500",
      "create_time": 1728716132,
      "id": 38,
      "identity_name": "owOYjs1YoOobY4ani_G2g--uqoqU",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "广东"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 2,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/BVolsXtGvK6uibg2a25Iv7DWcMRXXkX91LuFNJbgXQyks50BImskQd0CpkNEze2F9p23NjUjLE5M6pM3qJRTp7IobGk45pNfSxJRn07dNHsbytE91sxYUibl3NeAEPPzWb/64",
      "my_id": 340,
      "nick_name": "长歌行",
      "openid": "owOYjs1YoOobY4ani_G2g--uqoqU",
      "reply_new": {
        "max_reply_id": 2,
        "reply_list": [
          {
            "author_like_status": 0,
            "content": "而我想了想自己做近视手术市，对眼前全自动设备的担忧瞬间让我共情了。这就是站在创造者和使用者不同角度带来的不同感受。老年人还是希望只能真切的人类提供帮助",
            "create_time": **********,
            "identity_name": "owOYjs1YoOobY4ani_G2g--uqoqU",
            "identity_type": 0,
            "ip_wording": {
              "city_id": "",
              "city_name": "",
              "country_id": "156",
              "country_name": "中国",
              "province_id": "",
              "province_name": "广东"
            },
            "is_deleted": 0,
            "is_from": 3,
            "is_from_friend": 0,
            "logo_url": "http://wx.qlogo.cn/mmopen/BVolsXtGvK6uibg2a25Iv7DWcMRXXkX91LuFNJbgXQyks50BImskQd0CpkNEze2F9p23NjUjLE5M6pM3qJRTp7IobGk45pNfSxJRn07dNHsbytE91sxYUibl3NeAEPPzWb/64",
            "nick_name": "长歌行",
            "openid": "owOYjs1YoOobY4ani_G2g--uqoqU",
            "reply_del_flag": 0,
            "reply_id": 1,
            "reply_is_elected": 1,
            "reply_like_num": 1,
            "reply_like_status": 0
          }
        ],
        "reply_total_cnt": 1
      }
    },
    {
      "author_like_status": 0,
      "content": "我现在的城市三分之一是电动车，作为路人，两种车在身边的声音差不多。当然在车里，电动车比油车安静很多。",
      "content_id": "11383315238951584124",
      "create_time": **********,
      "id": 36,
      "identity_name": "owOYjs_j-QyW5hG2bvMBa9uqavBs",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "广东"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 2,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/ajNVdqHZLLBQkUT4C7z8gVSGJdp1dxicMP95ylDmLQTT0ljYZ1OPxJkZk55ib834CK4uOncHLI4akD0rD7zV5MrqYbhalrnRsesqsqg0Rf7IKzfvUf1EslKicxSRiaGowYXU/64",
      "my_id": 380,
      "nick_name": "星亮Tiger",
      "openid": "owOYjs_j-QyW5hG2bvMBa9uqavBs",
      "reply_new": {
        "max_reply_id": 1,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    },
    {
      "author_like_status": 0,
      "content": "现在有很多24小时台球厅就是使用“乒乓仓”的这个模式，用户通过APP开台，可以付费录像，到时间就关灯系统检查你是否归还了台球，确认无误后退还押金。\n有一个品牌叫作“球小闹”，已经开了5000家加盟店。",
      "content_id": "6326511673393807652",
      "create_time": 1728639728,
      "id": 34,
      "identity_name": "owOYjs01MRI1mnmtkE3DZUBGpGA4",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "辽宁"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 2,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/A0017IuWaqwMsDhKLw0HyECArc4cbrSLWSkibMg6cZXN8icIichH3iceiaud9spsgTCaR687FpxiaibDspDXBQ6IVw0eTzw8OhTM31y/64",
      "my_id": 292,
      "nick_name": "唐克",
      "openid": "owOYjs01MRI1mnmtkE3DZUBGpGA4",
      "reply_new": {
        "max_reply_id": 1,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    },
    {
      "author_like_status": 0,
      "content": "共享阅览室，自习室，台球室，私人电影院，KTV差不多都是这种模式。但乒乓球室共享是伪需求，国内的公园，老年活动室都会放几张台子，而且打乒乓球的中老年更多。还有一点，打乒乓球除了事业编糊弄糊弄领导，如果有更好的运动选择，它是第一个被放弃的（不够优雅也不够酷）。",
      "content_id": "12814284465215897873",
      "create_time": 1728617530,
      "id": 28,
      "identity_name": "v2_060000231003b20faec8cae18f1dcbd1c702e933b077fc40949ce0fde50e1c3e71c6de493b26@finder",
      "identity_type": 2,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "北京"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 2,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/finderhead/Q3auHgzwzM7WtK1R2pPAwhMVY2HGC9lo9xIZLN9RmfiafcWGMfpvnkg/64",
      "my_id": 273,
      "nick_name": "Jerry的小摩托",
      "openid": "owOYjs7qbOlkY1YQrmSjnPKEqGyI",
      "reply_new": {
        "max_reply_id": 1,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    },
    {
      "author_like_status": 0,
      "content": "巨型望远镜也用于未知的世界，软件合成处理的，对未知的信息可能更容易丢失",
      "content_id": "14700118971831549964",
      "create_time": 1728614542,
      "id": 27,
      "identity_name": "owOYjsx8Z8dv7WwdMZ8LFxYbYYyo",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "河南"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 2,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/BVolsXtGvK6fPWj30r3om2iaW7Tt8qK6iclfFjnnXv8SxBIkXkU2IdVIEAlDyw06lBapic0gcJ8kRSsTYBfVRDDVX7krw8pso5R/64",
      "my_id": 12,
      "nick_name": "红矮星de虹引",
      "openid": "owOYjsx8Z8dv7WwdMZ8LFxYbYYyo",
      "reply_new": {
        "max_reply_id": 1,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    },
    {
      "author_like_status": 0,
      "content": "据我所知，乒乓仓还真有需求",
      "content_id": "5390280571769323957",
      "create_time": 1728610542,
      "id": 18,
      "identity_name": "3",
      "identity_type": 3,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "北京"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 2,
      "like_status": 0,
      "logo_url": "https://wx.qlogo.cn/mmopen/vi_32/PiajxSqBRaEKYC6mCCN8aiaaYqVnzzicuUyvzs58yYgAKP9OyY38wIHoTviaMg1L7C43eFbicwYdXXZ0QLIedY8xZlD2Fd6BBwXm0L4M90TfFwSmEB07MDGhcMg/64",
      "my_id": 437,
      "nick_name": "何时破三",
      "openid": "owOYjs7Vup0YDMQeHOhq4EhmzYPM",
      "reply_new": {
        "max_reply_id": 1,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    },
    {
      "author_like_status": 0,
      "content": "快乐的一天[得意]",
      "content_id": "7621998935149642760",
      "create_time": 1728609924,
      "id": 17,
      "identity_name": "owOYjs_ZgScpOX0iKKxWlWa04hfg",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "四川"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 0,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/A0017IuWaqxDXmqEibMLzRX7Dln3BEiaficiaccELyWZNfDswEdK3UMU2YvesNVZEoyWO07kDeNXI7Xn3srwBJCIFkYdpTkorHnF/64",
      "my_id": 1032,
      "nick_name": "闫志刚",
      "openid": "owOYjs_ZgScpOX0iKKxWlWa04hfg",
      "reply_new": {
        "max_reply_id": 3,
        "reply_list": [
          {
            "author_like_status": 0,
            "content": "明天还要上班",
            "create_time": 1728610103,
            "identity_name": "owOYjs48pvn1jiFUOgDktEY7WiKk",
            "identity_type": 0,
            "ip_wording": {
              "city_id": "",
              "city_name": "",
              "country_id": "156",
              "country_name": "中国",
              "province_id": "",
              "province_name": "北京"
            },
            "is_deleted": 0,
            "is_from": 3,
            "is_from_friend": 0,
            "logo_url": "http://wx.qlogo.cn/mmopen/PiajxSqBRaEIzutltUjdHR3AUJ4hnPTNzAA4KxWLjh63hYcrzvqDdoRuLqpPKibbmMyT0jawCjyiaTItcAy8dmtmBU2TuIrMFqbptXVvkiczoXhric7Is8icToBESa0vBeYZIq/64",
            "nick_name": "见路不走",
            "openid": "owOYjs48pvn1jiFUOgDktEY7WiKk",
            "reply_del_flag": 0,
            "reply_id": 1,
            "reply_is_elected": 1,
            "reply_like_num": 2,
            "reply_like_status": 0
          }
        ],
        "reply_total_cnt": 1
      }
    },
    {
      "author_like_status": 0,
      "content": "高速行驶时电动汽车也吵",
      "content_id": "71132711485440119",
      "create_time": 1728609242,
      "id": 13,
      "identity_name": "gh_232efb3e7a29",
      "identity_type": 1,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "344",
        "province_name": "中国香港"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 2,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM61DUDsdnXBGRRCDvCGdaqvoOicZMguozI1fjarQhjDvGw/64",
      "my_id": 119,
      "nick_name": "RustHub",
      "openid": "owOYjs95PZ151_Zb8kbpUvfLDus8",
      "reply_new": {
        "max_reply_id": 1,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    },
    {
      "author_like_status": 0,
      "content": "3年前我在上海用过那种台球室了",
      "content_id": "13834025235906764",
      "create_time": 1728608125,
      "id": 6,
      "identity_name": "owOYjs9liwL3ro_vfVRX46xgdvKc",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "上海"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 2,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/OU2rqvx645va8I3H2Oc7UHwPGfjicKMj5JKKlB69wTNdDGv8cUcqOQ6ib3KPaZzB5V0IibxrqVVFaOiaJf0v9kLSQvUKcTGgUDYwoP47bmQjicFDiaYjKFJnP9y6icRYhUY2Rgq/64",
      "my_id": 204,
      "nick_name": "马里奥Mark🍍",
      "openid": "owOYjs9liwL3ro_vfVRX46xgdvKc",
      "reply_new": {
        "max_reply_id": 1,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    },
    {
      "author_like_status": 0,
      "content": "跟国内加盟制的无人桌球室很像，就是供于写字楼人士闲暇之余放松的锻炼。",
      "content_id": "431925769829089325",
      "create_time": 1728636158,
      "id": 33,
      "identity_name": "gh_bc8f7e5a8461",
      "identity_type": 1,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "重庆"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 1,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmhead/uI5pczeERTblcCUJCAEPdD45OpE6LM9M929CJwhQJsNO61vny2bkzr67l7aViaMCBrJUZib4gnlxA/64",
      "my_id": 45,
      "nick_name": "些许观点",
      "openid": "owOYjs4QRaeHSb-kWSIeqYbvUjH0",
      "reply_new": {
        "max_reply_id": 1,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    },
    {
      "author_like_status": 0,
      "content": "如果有人给你一笔钱，让你准备一个商业计划，你会选择什么项目？\n\n很大可能，你根本想不出干什么。现在的商业计划太难了，所有领域的商业竞争空前激烈，几乎找不到高增长的领域。\n我觉得中国人多，勤劳智慧。\n最缺乏的是创意点子共享中心，商业化会乱。行政主导效率低。众筹竞拍等，又缺主体。\n但前景可观。试想如果能集成全人类的智慧，那可厉害了[微笑]",
      "content_id": "9708028400077112188",
      "create_time": 1728613845,
      "id": 26,
      "identity_name": "owOYjsxVjjq5UNgzbwvtNgDrcq5k",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "山西"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 1,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/A0017IuWaqwMsDhKLw0HyMjSHKibFibxwLdKGov1SL44kYr7SBLGkSmcEOb2FkrflvgWbLbw1knmLdspgeVvK9Mv2QeiaEvic99b/64",
      "my_id": 892,
      "nick_name": "今西sxcpjxb",
      "openid": "owOYjsxVjjq5UNgzbwvtNgDrcq5k",
      "reply_new": {
        "max_reply_id": 2,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    },
    {
      "author_like_status": 0,
      "content": "封面图是甘肃省民勤县腾格里沙漠上的雕塑",
      "content_id": "11425708224020480059",
      "create_time": 1728611547,
      "id": 22,
      "identity_name": "owOYjs8VGkjnr7BcwSciaH3H-7NM",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": ""
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 1,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/8MnTI7HjgENujwTXhictB6piaxiaFrb8z9coJJash8gdsjuq838G45Gs6jYb8ibbYDUmiakK0IDqFGsBZwQnPKUEbSib07pRVmcrVA/64",
      "my_id": 59,
      "nick_name": "Ida",
      "openid": "owOYjs8VGkjnr7BcwSciaH3H-7NM",
      "reply_new": {
        "max_reply_id": 1,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    },
    {
      "author_like_status": 0,
      "content": "乒乓球创业项目难，首先是经济形势，其次是社会氛围如果真的办下来有很大一个成本要解决被当成免费宿舍问题",
      "content_id": "13555389236873527739",
      "create_time": 1728611221,
      "id": 19,
      "identity_name": "owOYjs7XQSPFyVaKG0sCuoikmgZk",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "浙江"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 1,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/BVolsXtGvK7g1ibyQEYQchmVLROyjZhN5UyWoFtWfffoOQSKe398Nmz97WNacsnuHx3fvhtB5UJ16YVmNLeU9B3umzlKvaKw0/64",
      "my_id": 443,
      "nick_name": "ihdoB",
      "openid": "owOYjs7XQSPFyVaKG0sCuoikmgZk",
      "reply_new": {
        "max_reply_id": 1,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    },
    {
      "author_like_status": 0,
      "content": "本次文摘的第12条和第69条，可以综合起来看",
      "content_id": "958970848922828900",
      "create_time": 1728609648,
      "id": 16,
      "identity_name": "owOYjswhRmGwBUP63aXvUPe1bggc",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "上海"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 1,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/PiajxSqBRaELB1O8SHMMFgVlFI92K8PLsrriaskxrzbicnLMKaOiaOMN5roMnDJEmKnurtflspdenb5l0nNIicKQcDw/64",
      "my_id": 100,
      "nick_name": "世上荷风",
      "openid": "owOYjswhRmGwBUP63aXvUPe1bggc",
      "reply_new": {
        "max_reply_id": 1,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    },
    {
      "author_like_status": 0,
      "content": "空置无所谓，园区赚国家的💰，不是租赁费用",
      "content_id": "3359990969071042814",
      "create_time": 1728607775,
      "id": 4,
      "identity_name": "owOYjs9-ugtwvqis-RR1-NZXdqok",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "江苏"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 1,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/OU2rqvx645usRFUYsNoyicDb6J3VdeoHXxUiax38tSgg5BKGnKyWvCZuForssbibibyyIBb564ocDgNbnwxUnzyPwQ/64",
      "my_id": 254,
      "nick_name": "黄云龙",
      "openid": "owOYjs9-ugtwvqis-RR1-NZXdqok",
      "reply_new": {
        "max_reply_id": 1,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    },
    {
      "author_like_status": 0,
      "content": "无人乒乓球房这个点子，其实在在2022年9月国内就有人提出，并且已经在做了，而且在一段时间里流水净利润都很可观，但是后面终究是难以为继。\n原因很简单：\n一是无人值守，是新奇的，人们猎奇有三分钟热度，但并非刚需。\n二是国内房价并不友好，需求不常有，但房租时时刻刻有。而且一旦有气色，房东绝对是第一个找上门的。\n三是基础设施维护成本高，越久越高。而且偷砸损一直会有，各种维权时间成本高。\n四是高价无人来，终究是薄利市场，还不如几场美女直播，大部分资本家看不上。",
      "content_id": "3210891076311187483",
      "create_time": 1728726026,
      "id": 39,
      "identity_name": "6",
      "identity_type": 3,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "广东"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 0,
      "like_status": 0,
      "logo_url": "https://wx.qlogo.cn/mmopen/vi_32/60EDC1Z9latDt3IDYACCKoRHUvkYoK756HFTibpJPoF4b1pAwLrj9Wia1Ue7XEaQdPClhsW11IneibHRfF5d9ib3wA/64",
      "my_id": 27,
      "nick_name": "lawyerCH",
      "openid": "owOYjs3lJrR5atNVKD50aGSaZ9MY",
      "reply_new": {
        "max_reply_id": 1,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    },
    {
      "author_like_status": 0,
      "content": "[71]电动汽车没有燃油机的噪音，但电机发出的细腻持续的嗡嗡声也是部分人群很敏感的，尤其夜晚安静的时候😪&nbsp;，还不如燃油机轰隆隆来的痛快。",
      "content_id": "11056806641084137623",
      "create_time": 1728705966,
      "id": 37,
      "identity_name": "owOYjsxDMOasU0PYyak8AdeMoUmY",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "北京"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 0,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/A0017IuWaqzgrxKaS7vjsyGJdz974xfAOibLQ9r5FW6frpGvZEgM57WquudUwaU2ZlBTVvca3rZBKmJmJISKiaTBkgaLVibpVxxbwur5jW7WgHOabkU9DTOicLK8LRXiazjuM/64",
      "my_id": 151,
      "nick_name": "Moonmen",
      "openid": "owOYjsxDMOasU0PYyak8AdeMoUmY",
      "reply_new": {
        "max_reply_id": 1,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    },
    {
      "author_like_status": 0,
      "content": "石头变食物的技术不错，未来限制人类发展的是能源和自然资源",
      "content_id": "8843836424131184091",
      "create_time": 1728618560,
      "id": 30,
      "identity_name": "v2_060000231003b20faec8c5e48c19c2d4cb0ded30b077087416c324379ed9bb52df66bc91c64e@finder",
      "identity_type": 2,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": ""
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 0,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/finderhead/gWicbXPiajJn9oFX8l8HibRuNPicR6JmM2nO3oWOp03MFFIibdx1be1rB2w/64",
      "my_id": 1499,
      "nick_name": "不知道是不是可以了就是这个",
      "openid": "owOYjswZKmb55-bdf38n-3CMGqHg",
      "reply_new": {
        "max_reply_id": 1,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    },
    {
      "author_like_status": 0,
      "content": "调研一下国内的付费自习室，就知道能不能做了，我感觉前景不是那么好",
      "content_id": "1026162996633665546",
      "create_time": **********,
      "id": 29,
      "identity_name": "owOYjs8ZYKTkhIOutbpe6GiboijE",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "广东"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 0,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/Q3auHgzwzM4iakyaraDOoZM6TiaoCsOunGAzLJ2Y9ZZibvianPNRClhaicVrCc6SSvPUpmCavCy5CbJtEGPrbZ6XgEibEtKNqlTpnQvUxY8RrII155ubNHiaF7LhgibWjwxicHG3Q/64",
      "my_id": 10,
      "nick_name": "roxy",
      "openid": "owOYjs8ZYKTkhIOutbpe6GiboijE",
      "reply_new": {
        "max_reply_id": 1,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    },
    {
      "author_like_status": 0,
      "content": "小废物机器人🤖哈哈哈好玩的名字",
      "content_id": "7001255137289175197",
      "create_time": 1728613280,
      "id": 25,
      "identity_name": "owOYjs5IBdZrwgmN2Ybn12-Lj2Fo",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "天津"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 0,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/8MnTI7HjgENujwTXhictB6ticd9KVaLlsaGaeOllYEztqcQAXRbocZFhpUr0YibibQicNVX6icF4ZictrGibveQt1cQhJkMmDjzW639q/64",
      "my_id": 157,
      "nick_name": "雷",
      "openid": "owOYjs5IBdZrwgmN2Ybn12-Lj2Fo",
      "reply_new": {
        "max_reply_id": 1,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    }
  ],
  "elected_comment_total_cnt": 35,
  "enabled": 1,
  "friend_comment": [],
  "identity_name": "gh_ecb7803f8bb9",
  "identity_type": 1,
  "is_fans": 1,
  "logo_url": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM5PoG8G9gPdA6cQGuU2eG1aAymPuyRasDVyMPw6Fpq9Qg/132",
  "my_comment": [],
  "nick_name": "xkiller",
  "not_pay_can_comment": 0,
  "only_fans_can_comment": false,
  "only_fans_days_can_comment": false,
  "reply_flag": 2
}

const commentData2 = {
  "base_resp": {
    "exportkey_token": "",
    "ret": 0
  },
  "buffer": "GA8wAA==",
  "continue_flag": true,
  "elected_comment": [
    {
      "author_like_status": 0,
      "content": "四年过去了，你把这算法闹明白了吗[机智]",
      "content_id": "5202285803069243886",
      "create_time": 1730861270,
      "id": 5,
      "identity_name": "oHLtNwB2lBKmA5x-hs7JMy99nQ1E",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "四川"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 1,
      "like_num": 16,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/ryGpckCTdr3U7Yqw8nPJ4jFfF2esmibPPlR9bfNMtZcwfY1S1FibVibXzowZ5drgftvtGzhlZkRSmcmCjUkHVuROOotEfdJqFc8/64",
      "my_id": 494,
      "nick_name": "轩辕之风",
      "openid": "oHLtNwB2lBKmA5x-hs7JMy99nQ1E",
      "reply_new": {
        "max_reply_id": 1,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    },
    {
      "author_like_status": 0,
      "content": "让动态规划算法再次伟大",
      "content_id": "1522936476210301210",
      "create_time": 1730861225,
      "id": 2,
      "identity_name": "2",
      "identity_type": 3,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "上海"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 22,
      "like_status": 0,
      "logo_url": "https://wx.qlogo.cn/mmopen/vi_32/Q3auHgzwzM7PpIFNNvE1lsyK5ETarw6waOd3YYiaJibc7OzBL3dwx52mG3HtVAw7uiaic8WZE4YXF6c70BjwmUfzFQ/64",
      "my_id": 2330,
      "nick_name": "momo",
      "openid": "oHLtNwB4718oRilJzdGRXh3SyNzM",
      "reply_new": {
        "max_reply_id": 1,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    },
    {
      "author_like_status": 0,
      "content": "实际上&nbsp;all_states&nbsp;变量应该为&nbsp;all_swing_states，只需要考虑七个摇摆州就行，懂王实际行程也是在这几个州来回飞[呲牙]",
      "content_id": "9475364262101647372",
      "create_time": 1730863585,
      "id": 12,
      "identity_name": "oHLtNwF4F5y1ZksIJd7gAo6leVcQ",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "广东"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 5,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/ajNVdqHZLLAR3gKNgWANvNVzKoNAiacrYVVaic7iaOc3Dc3VIluUu7JjZIe0Nov5lnMze5J0VSmjVLiaH9lpvXKd1ATUibjIugakLe8HV30ibyNLE0ePVJWpzsdBckMy6PW9lY/64",
      "my_id": 12,
      "nick_name": "Fergie🛩",
      "openid": "oHLtNwF4F5y1ZksIJd7gAo6leVcQ",
      "reply_new": {
        "max_reply_id": 2,
        "reply_list": [
          {
            "author_like_status": 0,
            "content": "是的",
            "create_time": 1730863751,
            "identity_name": "gh_fe02be2b2ff0",
            "identity_type": 1,
            "is_deleted": 0,
            "is_from": 2,
            "is_from_friend": 0,
            "logo_url": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM5iakZHNmMDuPl0azqlm8pPVtxhYTNqISsiat1OwT8f4I7Q/64",
            "nick_name": "轩辕的编程宇宙",
            "openid": "",
            "reply_del_flag": 0,
            "reply_id": 1,
            "reply_is_elected": 1,
            "reply_like_num": 1,
            "reply_like_status": 0
          }
        ],
        "reply_total_cnt": 1
      }
    },
    {
      "author_like_status": 0,
      "content": "加州不会去的必蓝",
      "content_id": "6908521763961831452",
      "create_time": 1730861597,
      "id": 7,
      "identity_name": "oHLtNwFG1XmgoY1-2xIV5tXiXj_E",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "山东"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 5,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/KydxAIB52xlW8xfaW2rFuAZNRSG1zUkzDkgO6rvAKeaNKuXT25m67lHIsYAEtnkV4jFFAW0XXhJJLIRGeLC3ra7WQoDV6vv96UgNz6mQrPbKjhD4ibDKjPBZAv1l5p98b/64",
      "my_id": 28,
      "nick_name": "Curiosity",
      "openid": "oHLtNwFG1XmgoY1-2xIV5tXiXj_E",
      "reply_new": {
        "max_reply_id": 1,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    },
    {
      "author_like_status": 0,
      "content": "建国同志现在178&nbsp;哈牛逼99[Emm]",
      "content_id": "3876171640626741729",
      "create_time": 1730861313,
      "id": 6,
      "identity_name": "oHLtNwG6qh3K4HUTg-7tevOILchg",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "安徽"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 5,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/PiajxSqBRaELL3ERsAYkpGCAp5GbPox6gvMMdN8zFNFwkNtXic7hvPCZZOslX7vobVqgV6euKrCib6EVAB6nPrkGcahUGiavqL9tgyNFhga92aOr7Za0BNjyReR7aob4qukic/64",
      "my_id": 481,
      "nick_name": "遇见幸福",
      "openid": "oHLtNwG6qh3K4HUTg-7tevOILchg",
      "reply_new": {
        "max_reply_id": 3,
        "reply_list": [
          {
            "author_like_status": 0,
            "content": "加州54深蓝还没开",
            "create_time": 1730861536,
            "identity_name": "4",
            "identity_type": 3,
            "ip_wording": {
              "city_id": "",
              "city_name": "",
              "country_id": "156",
              "country_name": "中国",
              "province_id": "",
              "province_name": "上海"
            },
            "is_deleted": 0,
            "is_from": 3,
            "is_from_friend": 0,
            "logo_url": "https://wx.qlogo.cn/mmopen/vi_32/Q3auHgzwzM5x3kibWsE7CicFTxAgCQKPHEIkZAygvzDxsqBkmFY8LcGLgG764cl7MibPIWay1n6Fs0icfCRURrQXAw/64",
            "nick_name": "阿白",
            "openid": "oHLtNwHt-uFQZ5DHFhxAtqe6-urg",
            "reply_del_flag": 0,
            "reply_id": 2,
            "reply_is_elected": 1,
            "reply_like_num": 5,
            "reply_like_status": 0
          }
        ],
        "reply_total_cnt": 1
      }
    },
    {
      "author_like_status": 0,
      "content": "懂王：没有人比我更懂动态规划",
      "content_id": "2368798845996892199",
      "create_time": 1730864105,
      "id": 13,
      "identity_name": "oHLtNwENH9kSeepAZfaQuwO0up7s",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "广东"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 4,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/DD6w1w6XNvhdCJlibbnpmh56LNlic8bgw9XjqPVJCnh0naHN8mxrVPpvKrzvXfyicOCmfZuhFggBHLrCvcFEMNmNDDgIk3XfbdR/64",
      "my_id": 39,
      "nick_name": "义勇",
      "openid": "oHLtNwENH9kSeepAZfaQuwO0up7s",
      "reply_new": {
        "max_reply_id": 1,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    },
    {
      "author_like_status": 0,
      "content": "有汇编语言版本的么？[害羞]",
      "content_id": "10252711080199455715",
      "create_time": 1730866610,
      "id": 17,
      "identity_name": "oHLtNwNK3_xkakUsbE9gtESea_Tc",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "广东"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 2,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/ryGpckCTdr3U7Yqw8nPJ4kJprNcF74dS4Zp4ocK3LHyvAxanf5AbgRU6mjqkHJj8jUnibJsYvKt2hVTIPGrKIlGUzhJ10uEbL/64",
      "my_id": 995,
      "nick_name": "习习惠风",
      "openid": "oHLtNwNK3_xkakUsbE9gtESea_Tc",
      "reply_new": {
        "max_reply_id": 1,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    },
    {
      "author_like_status": 0,
      "content": "要是再加上每个州花费时间和概率的关系[让我看看]",
      "content_id": "10536588283658371263",
      "create_time": 1730865447,
      "id": 15,
      "identity_name": "oHLtNwApO_H0XZAp4WcMKnzq_r58",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "四川"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 2,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/ryGpckCTdr2HhiaLl3SQjW4I3MjcQZxUYEkk5XbHsevl6sJZV4hfYSHbSynhz5rlEXt5ssSk6veJga6DMoG9vWBmfxPrw3thr/64",
      "my_id": 191,
      "nick_name": "JouLin",
      "openid": "oHLtNwApO_H0XZAp4WcMKnzq_r58",
      "reply_new": {
        "max_reply_id": 1,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    },
    {
      "author_like_status": 0,
      "content": "最终还是懂王抗下了所有[捂脸]",
      "content_id": "10503893068455871074",
      "create_time": 1730861933,
      "id": 8,
      "identity_name": "oHLtNwLQCdKah98vcerErKMwDh5Q",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "安徽"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 2,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/DD6w1w6XNvjykQd62qu1HeibopgXcScJ9uIELeTW1niaKnbReI2mbZKQLONU3nW1icbBdMnt3wSmxfMLf7fFib596tARo0DdWJ5q/64",
      "my_id": 610,
      "nick_name": "Nick",
      "openid": "oHLtNwLQCdKah98vcerErKMwDh5Q",
      "reply_new": {
        "max_reply_id": 1,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    },
    {
      "author_like_status": 0,
      "content": "不如让黏菌来找个路径比较好",
      "content_id": "11964401765522079781",
      "create_time": 1730863303,
      "id": 11,
      "identity_name": "oHLtNwAZB-vwax_zLdLoctVk3dk8",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "湖北"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 1,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/DD6w1w6XNvgFLrv6GvbGDQBSoUMiapRTxsXwPWqxFsTHBn37ChSDaoCibZVbCyOMwiaS8OoXicAkljlBJsw8VEYuDg3NU6nKsJic7/64",
      "my_id": 37,
      "nick_name": "kknd",
      "openid": "oHLtNwAZB-vwax_zLdLoctVk3dk8",
      "reply_new": {
        "max_reply_id": 2,
        "reply_list": [
          {
            "author_like_status": 0,
            "content": "生物规划[呲牙]",
            "create_time": 1730867771,
            "identity_name": "oHLtNwBDNsB5Y7TsA2U4znSIjX1A",
            "identity_type": 0,
            "ip_wording": {
              "city_id": "",
              "city_name": "",
              "country_id": "156",
              "country_name": "中国",
              "province_id": "",
              "province_name": "天津"
            },
            "is_deleted": 0,
            "is_from": 3,
            "is_from_friend": 0,
            "logo_url": "http://wx.qlogo.cn/mmopen/ryGpckCTdr01hNMsqoXm9PRhR6xibIQOg0eibSjgh0xCMBy2ewjib87CqPVnGmpYia7b9ZjWZubLOgM3ALAgGXaq8sOGNHc0ZiaalMlh6S3cANk1qbY6FSYPUXmtTDWaLLBUn/64",
            "nick_name": "kali",
            "openid": "oHLtNwBDNsB5Y7TsA2U4znSIjX1A",
            "reply_del_flag": 0,
            "reply_id": 1,
            "reply_is_elected": 1,
            "reply_like_num": 0,
            "reply_like_status": 0
          }
        ],
        "reply_total_cnt": 1
      }
    },
    {
      "author_like_status": 0,
      "content": "没有人比我更懂.jpg",
      "content_id": "14470827903601869481",
      "create_time": 1730861258,
      "id": 4,
      "identity_name": "gh_72941b4b34ec",
      "identity_type": 1,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "广东"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 1,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM4wT1LH2M1BK32eQJZcV1lqGfuruUTTa1ibqnfLBMe6QwA/64",
      "my_id": 681,
      "nick_name": "草木树心",
      "openid": "oHLtNwOarRQVSQi9O1FlaqshriL0",
      "reply_new": {
        "max_reply_id": 1,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    },
    {
      "author_like_status": 0,
      "content": "真学以致用了",
      "content_id": "10495743642889617566",
      "create_time": 1730867796,
      "id": 19,
      "identity_name": "oHLtNwBDNsB5Y7TsA2U4znSIjX1A",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "天津"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 0,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/ryGpckCTdr01hNMsqoXm9PRhR6xibIQOg0eibSjgh0xCMBy2ewjib87CqPVnGmpYia7b9ZjWZubLOgM3ALAgGXaq8sOGNHc0ZiaalMlh6S3cANk1qbY6FSYPUXmtTDWaLLBUn/64",
      "my_id": 158,
      "nick_name": "kali",
      "openid": "oHLtNwBDNsB5Y7TsA2U4znSIjX1A",
      "reply_new": {
        "max_reply_id": 1,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    },
    {
      "author_like_status": 0,
      "content": "赢了啊",
      "content_id": "7904188783396914072",
      "create_time": 1730867389,
      "id": 18,
      "identity_name": "2",
      "identity_type": 3,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "上海"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 0,
      "like_status": 0,
      "logo_url": "https://wx.qlogo.cn/mmopen/vi_32/Q3auHgzwzM7PpIFNNvE1lsyK5ETarw6ws4DHGzw1h34UEHicWUfKe1GnoO2X1XS6IvaBVWibElMicibNfAnCRCsgPA/64",
      "my_id": 920,
      "nick_name": "momo",
      "openid": "oHLtNwOJQ07DNttseEWWkvr6bpB8",
      "reply_new": {
        "max_reply_id": 1,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    },
    {
      "author_like_status": 0,
      "content": "MAKE&nbsp;🪿麦瑞咖&nbsp;GREAT&nbsp;AGAIN!",
      "content_id": "358340138096394407",
      "create_time": 1730862975,
      "id": 10,
      "identity_name": "oHLtNwKczpKQD25u4nAf94wAC33Q",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": "上海"
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 0,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/ryGpckCTdr0IzxDPHXwUJQfz30uvkngSJ7HZOhUbT2G6de8hto6d3AXmIWV4SRPRgz0zybribL4REwHzTAX4gqibYvJ5SAftlo6wXM5wITPJtJcPUpb2MpjPToXVpRUnHj/64",
      "my_id": 167,
      "nick_name": "ninja",
      "openid": "oHLtNwKczpKQD25u4nAf94wAC33Q",
      "reply_new": {
        "max_reply_id": 1,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    },
    {
      "author_like_status": 0,
      "content": "已阅",
      "content_id": "5173123086798553492",
      "create_time": 1730861230,
      "id": 3,
      "identity_name": "oHLtNwLZBnWVCszImOAjh9V2rdlI",
      "identity_type": 0,
      "ip_wording": {
        "city_id": "",
        "city_name": "",
        "country_id": "156",
        "country_name": "中国",
        "province_id": "",
        "province_name": ""
      },
      "is_can_delete": false,
      "is_elected": 1,
      "is_from": 0,
      "is_from_friend": 0,
      "is_from_me": 0,
      "is_top": 0,
      "like_num": 0,
      "like_status": 0,
      "logo_url": "http://wx.qlogo.cn/mmopen/I0Ptg1XcgGialibjKqQ254iczz4sPCoguBy2SmOGZ5nTrwW5HJEHicaeqmQlRVFPstib4MP07usoAlazVgJiaaKd8bbYQdYQIYwbbFVfgHK7W8oVCqdcic2DiayLojk2UdvZqvQN/64",
      "my_id": 404,
      "nick_name": "脑壳方",
      "openid": "oHLtNwLZBnWVCszImOAjh9V2rdlI",
      "reply_new": {
        "max_reply_id": 1,
        "reply_list": [],
        "reply_total_cnt": 0
      }
    }
  ],
  "elected_comment_total_cnt": 15,
  "enabled": 1,
  "friend_comment": [],
  "identity_name": "gh_ecb7803f8bb9",
  "identity_type": 1,
  "is_fans": 1,
  "logo_url": "http://wx.qlogo.cn/mmhead/Q3auHgzwzM5PoG8G9gPdA6cQGuU2eG1aAymPuyRasDVyMPw6Fpq9Qg/132",
  "my_comment": [],
  "nick_name": "xkiller",
  "not_pay_can_comment": 0,
  "only_fans_can_comment": false,
  "only_fans_days_can_comment": false,
  "reply_flag": 2
}

const targetCommentData = commentData2

const totalCount = targetCommentData.elected_comment.length + targetCommentData.elected_comment.reduce((total, item) => {
  return total + item.reply_new.reply_total_cnt
}, 0)



</script>
